package com.dock.domain.vo;

import com.dock.domain.MStore;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 店铺视图对象 m_store
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MStore.class)
public class MStoreVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 所属商户ID（无外键约束）
     */
    @ExcelProperty(value = "所属商户ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "无=外键约束")
    private Long merchantId;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 店铺电话
     */
    @ExcelProperty(value = "店铺电话")
    private String storePhone;

    /**
     * 店铺地址
     */
    @ExcelProperty(value = "店铺地址")
    private String storeAddress;

    private String title;

    private String headLine;


}
