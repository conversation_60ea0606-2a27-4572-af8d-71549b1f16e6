package com.dock.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dock.domain.MBusinessesVideo;
import com.dock.domain.req.StoreVideoBindRequest;
import com.dock.mapper.MBusinessesVideoMapper;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysOssUploadVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MBusinessesVideoVo;
import com.dock.domain.bo.MBusinessesVideoBo;
import com.dock.service.IMBusinessesVideoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频素材管理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/businesses/businessesVideo")
public class MBusinessesVideoController extends BaseController {

    private final IMBusinessesVideoService mBusinessesVideoService;
    private final ISysOssService ossService;
    private final MBusinessesVideoMapper mBusinessesVideoMapper;

    /**
     * 查询视频素材管理列表
     */
    @SaCheckPermission("businesses:businessesVideo:list")
    @GetMapping("/list")
    public TableDataInfo<MBusinessesVideoVo> list(MBusinessesVideoBo bo, PageQuery pageQuery) {
        return mBusinessesVideoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出视频素材管理列表
     */
    @SaCheckPermission("businesses:businessesVideo:export")
    @Log(title = "视频素材管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MBusinessesVideoBo bo, HttpServletResponse response) {
        List<MBusinessesVideoVo> list = mBusinessesVideoService.queryList(bo);
        ExcelUtil.exportExcel(list, "视频素材管理", MBusinessesVideoVo.class, response);
    }

    /**
     * 获取视频素材管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("businesses:businessesVideo:query")
    @GetMapping("/{id}")
    public R<MBusinessesVideoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mBusinessesVideoService.queryById(id));
    }

    /**
     * 新增视频素材管理
     */
    @SaCheckPermission("businesses:businessesVideo:add")
    @Log(title = "视频素材管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MBusinessesVideoBo bo) {
        return toAjax(mBusinessesVideoService.insertByBo(bo));
    }

    /**
     * 修改视频素材管理
     */
    @SaCheckPermission("businesses:businessesVideo:edit")
    @Log(title = "视频素材管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MBusinessesVideoBo bo) {
        return toAjax(mBusinessesVideoService.updateByBo(bo));
    }

    /**
     * 删除视频素材管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("businesses:businessesVideo:remove")
    @Log(title = "视频素材管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mBusinessesVideoService.deleteWithValidByIds(List.of(ids), true));
    }



    /**
     * 上传视频素材信息
     */

    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysOssUploadVo> upload(@RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        long fileSizeInMB = file.getSize() / (1024 * 1024);
        if (fileSizeInMB > 1024) { // 限制1G
            return R.fail("上传文件不能大于1G，请升级plus会员");
        }
        SysOssVo oss = ossService.upload(file);
        SysOssUploadVo uploadVo = new SysOssUploadVo();
        uploadVo.setUrl(oss.getUrl());
        uploadVo.setFileName(oss.getOriginalName());
        uploadVo.setOssId(oss.getOssId().toString());
        return R.ok(uploadVo);
    }


    /**
     * 查询当前用户所有视频
     * @return
     */
    @GetMapping("getVideoList")
    public R<List<Map<String, Object>>> getVideoList(){
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<MBusinessesVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MBusinessesVideo::getTenantId, tenantId);
        queryWrapper.select(MBusinessesVideo::getVideoUrl, MBusinessesVideo::getTitle, MBusinessesVideo::getDescription);
        List<MBusinessesVideo> list = mBusinessesVideoMapper.selectList(queryWrapper);

        List<Map<String, Object>> collect = list.stream()
                .filter(video -> Objects.nonNull(video.getVideoUrl()))
                .map(video -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("videoUrl", video.getVideoUrl());
                    map.put("title", video.getTitle());
                    map.put("description", video.getDescription());
                    return map;
                })
                .collect(Collectors.toList());

        return R.ok(collect);
    }




}

// da
// http://127.0.0.1:9000/media/1/2025/07/28/75e7a83f1195435b9c11726edcdf2bd7.mp4
// xiao
// http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4