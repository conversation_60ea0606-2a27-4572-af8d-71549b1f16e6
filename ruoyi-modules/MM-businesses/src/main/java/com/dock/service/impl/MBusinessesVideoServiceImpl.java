package com.dock.service.impl;

import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dock.domain.MStore;
import com.dock.domain.bo.MStoreBo;
import com.dock.mapper.MStoreMapper;
import com.dock.service.IMStoreService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MBusinessesVideoBo;
import com.dock.domain.vo.MBusinessesVideoVo;
import com.dock.domain.MBusinessesVideo;
import com.dock.mapper.MBusinessesVideoMapper;
import com.dock.service.IMBusinessesVideoService;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频素材管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MBusinessesVideoServiceImpl implements IMBusinessesVideoService {

    private final MBusinessesVideoMapper baseMapper;
    private final ObjectMapper objectMapper;
    private final IMStoreService imStoreService;
    private final MStoreMapper mStoreMapper;

    /**
     * 查询视频素材管理
     *
     * @param id 主键
     * @return 视频素材管理
     */
    @Override
    public MBusinessesVideoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询视频素材管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频素材管理分页列表
     */
    @Override
    public TableDataInfo<MBusinessesVideoVo> queryPageList(MBusinessesVideoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MBusinessesVideo> lqw = buildQueryWrapper(bo);
        Page<MBusinessesVideoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的视频素材管理列表
     *
     * @param bo 查询条件
     * @return 视频素材管理列表
     */
    @Override
    public List<MBusinessesVideoVo> queryList(MBusinessesVideoBo bo) {
        LambdaQueryWrapper<MBusinessesVideo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MBusinessesVideo> buildQueryWrapper(MBusinessesVideoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MBusinessesVideo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MBusinessesVideo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), MBusinessesVideo::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), MBusinessesVideo::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getVideoUrl()), MBusinessesVideo::getVideoUrl, bo.getVideoUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), MBusinessesVideo::getCategory, bo.getCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MBusinessesVideo::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增视频素材管理
     *
     * @param bo 视频素材管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MBusinessesVideoBo bo) {
        MBusinessesVideo add = MapstructUtils.convert(bo, MBusinessesVideo.class);
        validEntityBeforeSave(add);
        bo.setId(null);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频素材管理
     *
     * @param bo 视频素材管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MBusinessesVideoBo bo) {
        MBusinessesVideo update = MapstructUtils.convert(bo, MBusinessesVideo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MBusinessesVideo entity) {
        String videoUrl = entity.getVideoUrl();

        if (videoUrl != null && !videoUrl.trim().isEmpty()) {
            try {
                // 获取视频文件大小
                URL url = new URL(videoUrl);
                URLConnection connection = url.openConnection();
                long fileSize = connection.getContentLengthLong(); // 获取 Content-Length

                // 校验单个视频大小
                if (fileSize >= 1073741824L) {
                    throw new ValidateException("允许的上传的视频大小为1G");
                }

                entity.setFileSize(fileSize);
                log.info("当前视频大小: {} bytes", fileSize);

                // 查询数据库中所有视频的 fileSize 总和
                LambdaQueryWrapper<MBusinessesVideo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(MBusinessesVideo::getFileSize);
                List<MBusinessesVideo> videoList = baseMapper.selectList(queryWrapper);

                long totalSize = videoList.stream()
                        .filter(Objects::nonNull)
                        .map(MBusinessesVideo::getFileSize)
                        .filter(Objects::nonNull)
                        // 如果是更新，排除当前这条（因为它还未更新）
                        .filter(size -> !Objects.equals(entity.getId(), null)) // 仅在新增时保留全部
                        .mapToLong(Long::longValue)
                        .sum();

                totalSize += fileSize; // 加上当前即将新增/更新的视频大小

                log.info("上传后总视频大小: {} bytes", totalSize);

                if (totalSize >= 1073741824L) {
                    throw new ValidateException("已上传视频总大小超过1G，无法继续上传");
                }

            } catch (IOException e) {
                throw new ServiceException("无法获取视频文件大小: " + e.getMessage());
            }
        }
    }

    /**
     * 校验并批量删除视频素材管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }



}
