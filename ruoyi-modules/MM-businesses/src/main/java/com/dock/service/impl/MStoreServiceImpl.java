package com.dock.service.impl;

import cn.hutool.core.exceptions.ValidateException;
import com.dock.domain.MMerchant;
import com.dock.mapper.MMerchantMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MStoreBo;
import com.dock.domain.vo.MStoreVo;
import com.dock.domain.MStore;
import com.dock.mapper.MStoreMapper;
import com.dock.service.IMStoreService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Collection;


/**
 * 店铺Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MStoreServiceImpl extends ServiceImpl<MStoreMapper, MStore> implements IMStoreService {

    private final MStoreMapper baseMapper;
    private final MMerchantMapper mMerchantMapper;
    private final ObjectMapper objectMapper;


    @Override
    public MStoreVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<MStoreVo> queryPageList(MStoreBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MStore> lqw = buildQueryWrapper(bo);
        Page<MStoreVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<MStoreVo> queryList(MStoreBo bo) {
        LambdaQueryWrapper<MStore> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MStore> buildQueryWrapper(MStoreBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MStore> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMerchantId() != null, MStore::getMerchantId, bo.getMerchantId());
        lqw.like(StringUtils.isNotBlank(bo.getStoreName()), MStore::getStoreName, bo.getStoreName());
        lqw.eq(StringUtils.isNotBlank(bo.getStorePhone()), MStore::getStorePhone, bo.getStorePhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStoreAddress()), MStore::getStoreAddress, bo.getStoreAddress());
        return lqw;
    }

    /**
     * 新增店铺
     *
     * @param bo 店铺
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MStoreBo bo) {

        LambdaQueryWrapper<MMerchant> mMerchantLambdaQueryWrapper = new LambdaQueryWrapper<>();
        mMerchantLambdaQueryWrapper.eq(MMerchant::getId, bo.getMerchantId());
        MMerchant merchant = mMerchantMapper.selectOne(mMerchantLambdaQueryWrapper);
        if (merchant == null) {
            throw new ServiceException("该商户不存在");
        }
        LambdaQueryWrapper<MStore> mStoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
        mStoreLambdaQueryWrapper.eq(MStore::getMerchantId, bo.getMerchantId());
        if (bo.getId() != null) {
            mStoreLambdaQueryWrapper.ne(MStore::getId, bo.getId());
        }
        Long count = baseMapper.selectCount(mStoreLambdaQueryWrapper);
        if (count >= 5) {
            throw new ServiceException("仅能管理5个店铺，请升级plus会员");
        }


        MStore add = MapstructUtils.convert(bo, MStore.class);
        validEntityBeforeSave(add);
        bo.setId(null);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改店铺
     *
     * @param bo 店铺
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MStoreBo bo) {
        MStore update = MapstructUtils.convert(bo, MStore.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验和 `videoUrl` 格式规范化
     */
    private void validEntityBeforeSave(MStore entity){
//        // 1. 商户存在性校验
//        LambdaQueryWrapper<MMerchant> mMerchantLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        mMerchantLambdaQueryWrapper.eq(MMerchant::getId, entity.getMerchantId());
//        MMerchant merchant = mMerchantMapper.selectOne(mMerchantLambdaQueryWrapper);
//        if (merchant == null) {
//            throw new ServiceException("该商户不存在");
//        }
//
//        // 2. 店铺数量限制校验
//        LambdaQueryWrapper<MStore> mStoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        mStoreLambdaQueryWrapper.eq(MStore::getMerchantId, entity.getMerchantId());
//        if (entity.getId() != null) {
//            mStoreLambdaQueryWrapper.ne(MStore::getId, entity.getId());
//        }
//        Long count = baseMapper.selectCount(mStoreLambdaQueryWrapper);
//        if (count >= 5) {
//            throw new ServiceException("仅能管理5个店铺，请升级plus会员");
//        }

        // 3. 视频URL格式规范化 (所有逻辑都内联在这里，无清洗)
        String rawVideoUrl = entity.getVideoUrl();
        if (StringUtils.isEmpty(rawVideoUrl)) { // org.dromara.common.core.utils.StringUtils.isEmpty() 检查 null 和 ""
//            entity.setVideoUrl(null); // 设置为null表示没有值
            return; // 提前退出，不执行后续的URL解析和序列化
        }
        List<String> videoUrlsList = new ArrayList<>();
        if (StringUtils.isNotEmpty(rawVideoUrl)) {
            if (rawVideoUrl.startsWith("[") && rawVideoUrl.endsWith("]")) {
                try {
                    videoUrlsList = objectMapper.readValue(rawVideoUrl, new TypeReference<List<String>>() {});
                } catch (JsonProcessingException e) {
                    if (rawVideoUrl.contains(",")) {
                        videoUrlsList = new ArrayList<>(Arrays.asList(rawVideoUrl.split(",")));
                    } else {
                        videoUrlsList.add(rawVideoUrl);
                    }
                }
            } else {
                // 如果不是JSON格式，则尝试逗号分隔或单个URL解析
                if (rawVideoUrl.contains(",")) {
                    videoUrlsList = new ArrayList<>(Arrays.asList(rawVideoUrl.split(","))); // No trimming/filtering
                } else {
                    videoUrlsList.add(rawVideoUrl); // No trimming/filtering
                }
            }
        }
        // 将规范化后的 List<String> 序列化回 JSON 字符串
        String normalizedVideoUrlJson;
        try {
            normalizedVideoUrlJson = objectMapper.writeValueAsString(videoUrlsList);
        } catch (JsonProcessingException e) {
            throw new ServiceException("视频URL数据处理失败：无法转换为JSON格式。");
        }

        entity.setVideoUrl(normalizedVideoUrlJson);
    }


    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
