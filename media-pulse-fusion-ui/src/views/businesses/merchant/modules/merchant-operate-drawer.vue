<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreateMerchant, fetchUpdateMerchant } from '@/service/api/businesses/merchant';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'MerchantOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Businesses.Merchant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增商户',
    edit: '编辑商户'
  };
  return titles[props.operateType];
});

type Model = Api.Businesses.MerchantOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      id: undefined,
      name: '',
      contactPhone: '',
      address: '',
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'contactPhone'
  | 'address'
  | 'tenantId'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  contactPhone: createRequiredRule('联系电话不能为空'),
  address: createRequiredRule('地址不能为空'),
  tenantId: createRequiredRule('租户ID，标记数据所属租户，可通过配置关闭不能为空'),
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, name, contactPhone, address } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateMerchant({ id, name, contactPhone, address });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateMerchant({ id, name, contactPhone, address });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="主键ID" path="id">
          <NInput v-model:value="model.id" placeholder="请输入主键ID" />
        </NFormItem>
        <NFormItem label="商户名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入商户名称" />
        </NFormItem>
        <NFormItem label="联系电话" path="contactPhone">
          <NInput v-model:value="model.contactPhone" placeholder="请输入联系电话" />
        </NFormItem>
        <NFormItem label="地址" path="address">
          <NInput v-model:value="model.address" placeholder="请输入地址" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
