<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NEmpty, NButton, useMessage } from 'naive-ui';

defineOptions({
  name: 'BusinessesVideoFormModule'
});

// 定义视频项类型
interface VideoItem {
  title: string;
  description: string;
  videoUrl: string;
}

// 响应式数据
const loading = ref(false);
const videoList = ref<VideoItem[]>([]);
const message = useMessage();

// 模拟API调用
async function fetchVideoListData() {
  // 模拟你提供的后端数据
  return {
    data: {
      code: 200,
      msg: "操作成功",
      data: [
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        },
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        },
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        },
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        },
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        },
        {
          videoUrl: "http://127.0.0.1:9000/media/1/2025/07/28/6534813d7d0e468ca09b82b9168414c7.mp4",
          description: "2",
          title: "1"
        }
      ]
    },
    error: null
  };
}

// 获取视频列表
async function getVideoList() {
  try {
    loading.value = true;
    const { data, error } = await fetchVideoListData();

    if (error) {
      message.error('获取视频列表失败');
      return;
    }

    if (data && data.code === 200) {
      videoList.value = data.data || [];
    } else {
      message.error(data?.msg || '获取视频列表失败');
    }
  } catch (err) {
    console.error('获取视频列表错误:', err);
    message.error('获取视频列表失败');
  } finally {
    loading.value = false;
  }
}

// 播放视频
function playVideo(videoUrl: string) {
  // 创建一个新窗口播放视频
  window.open(videoUrl, '_blank');
}

// 预览视频
function previewVideo(video: VideoItem) {
  console.log('预览视频:', video);
  // 这里可以实现视频预览逻辑，比如打开模态框
}

// 处理视频加载元数据
function handleVideoLoadedMetadata(event: Event) {
  const videoEl = event.target as HTMLVideoElement;
  videoEl.currentTime = 1; // 设置到第1秒获取缩略图
}

// 刷新列表
function refresh() {
  getVideoList();
}

// 暴露方法给父组件
defineExpose({
  refresh
});

// 组件挂载时获取数据
onMounted(() => {
  getVideoList();
});
</script>

<template>
  <div class="video-list-container">
    <NCard title="视频素材库" :bordered="false" size="small" class="card-wrapper">
      <template #header-extra>
        <NButton @click="refresh" :loading="loading" size="small">
          刷新
        </NButton>
      </template>

      <NSpin :show="loading">
        <div v-if="videoList.length === 0 && !loading" class="empty-container">
          <NEmpty description="暂无视频数据" />
        </div>

        <div v-else class="video-grid-container">
          <NGrid :cols="4" :x-gap="16" :y-gap="16" responsive="screen">
            <NGridItem
              v-for="(video, index) in videoList"
              :key="index"
              :span="1"
            >
              <div class="video-card">
                <div class="video-thumbnail">
                  <video
                    :src="video.videoUrl"
                    class="video-element"
                    preload="metadata"
                    muted
                    @loadedmetadata="handleVideoLoadedMetadata"
                  />
                  <div class="video-overlay">
                    <div class="video-actions">
                      <NButton
                        circle
                        type="primary"
                        size="large"
                        @click="playVideo(video.videoUrl)"
                        class="play-btn"
                      >
                        ▶
                      </NButton>
                      <NButton
                        circle
                        type="info"
                        size="medium"
                        @click="previewVideo(video)"
                        class="preview-btn"
                      >
                        👁
                      </NButton>
                    </div>
                  </div>
                </div>
                <div class="video-info">
                  <h4 class="video-title" :title="video.title">
                    {{ video.title }}
                  </h4>
                  <p class="video-description" :title="video.description">
                    {{ video.description }}
                  </p>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </NSpin>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
.video-list-container {
  height: 100%;

  .card-wrapper {
    height: 100%;

    :deep(.n-card__content) {
      height: calc(100% - 60px);
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.video-grid-container {
  min-height: 400px;
}

.video-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    .video-overlay {
      opacity: 1;
    }
  }
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 180px;
  background: #f5f5f5;
  overflow: hidden;

  .video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #000;
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .video-actions {
      display: flex;
      gap: 12px;
      align-items: center;

      .play-btn {
        background: rgba(24, 144, 255, 0.9);
        border: none;

        &:hover {
          background: rgba(24, 144, 255, 1);
          transform: scale(1.1);
        }
      }

      .preview-btn {
        background: rgba(255, 255, 255, 0.9);
        border: none;

        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }
      }
    }
  }
}

.video-info {
  padding: 16px;

  .video-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .video-description {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 42px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .video-grid-container {
    :deep(.n-grid) {
      grid-template-columns: repeat(3, 1fr) !important;
    }
  }
}

@media (max-width: 768px) {
  .video-grid-container {
    :deep(.n-grid) {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }

  .video-card {
    .video-thumbnail {
      height: 140px;
    }

    .video-info {
      padding: 12px;

      .video-title {
        font-size: 14px;
      }

      .video-description {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .video-grid-container {
    :deep(.n-grid) {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>

