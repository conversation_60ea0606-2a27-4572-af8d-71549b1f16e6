<script setup lang="ts">
import { ref } from 'vue';
import { useMessage } from 'naive-ui';
import BusinessesVideoUploadModule from './componets/businesses-video-upload-module.vue';
import BusinessesVideoFormModule from './componets/businesses-video-form-module.vue';

defineOptions({
  name: 'BusinessesVideoList'
});

const message = useMessage();

// 上传相关状态
const uploadFileList = ref<any[]>([]);
const isUploading = ref(false);
const showPreview = ref(false);

// 视频列表组件引用
const videoFormModuleRef = ref();

// 上传处理函数
async function handleUpload({ file, onFinish, onError }: { file: File; onFinish: () => void; onError: () => void }) {
  try {
    isUploading.value = true;
    console.log('开始上传文件:', file);

    // 模拟上传成功
    setTimeout(() => {
      message.success('视频上传成功！');
      onFinish();

      // 刷新视频列表
      if (videoFormModuleRef.value?.refresh) {
        videoFormModuleRef.value.refresh();
      }
      isUploading.value = false;
    }, 2000);

  } catch (err) {
    console.error('上传失败:', err);
    message.error('上传失败，请重试');
    onError();
    isUploading.value = false;
  }
}

// 文件预览处理
function handlePreview(file: any) {
  console.log('预览文件:', file);
}

// 文件删除处理
function handleRemove(file: any) {
  console.log('删除文件:', file);
}

// 显示预览弹窗
function handleShowPreview() {
  showPreview.value = true;
}
</script>

<template>
  <div class="businesses-video-page">
    <!-- 视频列表区域 -->
    <div class="video-list-section">
      <BusinessesVideoFormModule ref="videoFormModuleRef" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.businesses-video-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .upload-section {
    flex-shrink: 0;
    padding: 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
  }

  .video-list-section {
    flex: 1;
    overflow: hidden;
    padding: 16px;

    // 确保子组件可以正确滚动
    :deep(.video-list-container) {
      height: 100%;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .businesses-video-page {
    height: calc(100vh - 60px); // 减去移动端导航栏高度

    .upload-section {
      padding: 12px;
    }

    .video-list-section {
      padding: 12px;
    }
  }
}
</style>
