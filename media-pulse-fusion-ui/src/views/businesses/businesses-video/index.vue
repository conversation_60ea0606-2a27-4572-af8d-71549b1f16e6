<script setup lang="tsx">
import { ref } from 'vue';
import { NDivider, useMessage } from 'naive-ui';
import {
  fetchBatchDeleteBusinessesVideo,
  fetchGetBusinessesVideoList,
  fetchUploadVideo,
  fetchCreateBusinessesVideo
} from '@/service/api/businesses/businesses-video';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import BusinessesVideoUploadModule from '@/views/businesses/businesses-video/componets/businesses-video-upload-module.vue';
import BusinessesVideoFormModule from '@/views/businesses/businesses-video/componets/businesses-video-form-module.vue';

defineOptions({
  name: 'BusinessesVideoList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();
const message = useMessage();

// 上传相关状态
const uploadFileList = ref<any[]>([]);
const isUploading = ref(false);
const showPreview = ref(false);

// 视频列表组件引用
const videoFormModuleRef = ref();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetBusinessesVideoList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    title: null,
    description: null,
    videoUrl: null,
    category: null,
    status: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    // {
    //   key: 'id',
    //   title: '主键ID，视频素材唯一标识符',
    //   align: 'center',
    //   minWidth: 120
    // },
    {
      key: 'title',
      title: '视频标题',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'description',
      title: '视频素材详细描述',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'videoUrl',
      title: '视频文件存储的URL或路径，支持OSS/CDN',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'category',
      title: '视频素材分类，例如：教学、宣传、娱乐',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '素材状态',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('businesses:businessesVideo:edit') || !hasAuth('businesses:businessesVideo:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('businesses:businessesVideo:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('businesses:businessesVideo:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteBusinessesVideo(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteBusinessesVideo([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit(id);
}

function handleExport() {
  download('/businesses/businessesVideo/export', searchParams, `视频素材管理_${new Date().getTime()}.xlsx`);
}

// 上传处理函数
async function handleUpload({ file, onFinish, onError }: { file: File; onFinish: () => void; onError: () => void }) {
  try {
    isUploading.value = true;

    console.log('开始上传文件:', file);
    console.log('文件类型:', file.type);
    console.log('文件大小:', file.size);

    // 调用上传接口
    const { data, error } = await fetchUploadVideo(file);
    if (error) {
      console.error('上传接口错误:', error);
      message.error('上传失败: ' + (error.message || '未知错误'));
      onError();
      return;
    }

    console.log('上传成功，返回数据:', data);

    // 上传成功后，创建视频记录
    const videoData = {
      title: data.fileName,
      description: `上传的视频文件: ${data.fileName}`,
      videoUrl: data.ossId, // 使用 ossId 作为 videoUrl
      category: '上传视频',
      status: '1' // 启用状态
    };

    console.log('创建视频记录:', videoData);

    const { error: createError } = await fetchCreateBusinessesVideo(videoData);
    if (createError) {
      console.error('创建视频记录错误:', createError);
      message.error('创建视频记录失败: ' + (createError.message || '未知错误'));
      onError();
      return;
    }

    message.success('视频上传成功！');
    onFinish();

    // 刷新视频列表
    if (videoFormModuleRef.value?.refresh) {
      videoFormModuleRef.value.refresh();
    }

  } catch (err) {
    console.error('上传失败:', err);
    message.error('上传失败，请重试');
    onError();
  } finally {
    isUploading.value = false;
  }
}

// 文件预览处理
function handlePreview(file: any) {
  console.log('预览文件:', file);
  // 这里可以实现文件预览逻辑
}

// 文件删除处理
function handleRemove(file: any) {
  console.log('删除文件:', file);
  // 这里可以实现文件删除逻辑
}

// 显示预览弹窗
function handleShowPreview() {
  showPreview.value = true;
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
<!--    <BusinessesVideoSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />-->
<!--    <NCard title="视频素材管理列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">-->
<!--      <template #header-extra>-->
<!--        <TableHeaderOperation-->
<!--          v-model:columns="columnChecks"-->
<!--          :disabled-delete="checkedRowKeys.length === 0"-->
<!--          :loading="loading"-->
<!--          :show-add="hasAuth('businesses:businessesVideo:add')"-->
<!--          :show-delete="hasAuth('businesses:businessesVideo:remove')"-->
<!--          :show-export="hasAuth('businesses:businessesVideo:export')"-->
<!--          @add="handleAdd"-->
<!--          @delete="handleBatchDelete"-->
<!--          @export="handleExport"-->
<!--          @refresh="getData"-->
<!--        />-->
<!--      </template>-->
<!--      <NDataTable-->
<!--        v-model:checked-row-keys="checkedRowKeys"-->
<!--        :columns="columns"-->
<!--        :data="data"-->
<!--        size="small"-->
<!--        :flex-height="!appStore.isMobile"-->
<!--        :scroll-x="962"-->
<!--        :loading="loading"-->
<!--        remote-->
<!--        :row-key="row => row.id"-->
<!--        :pagination="mobilePagination"-->
<!--        class="sm:h-full"-->
<!--      />-->
<!--      <BusinessesVideoOperateDrawer-->
<!--        v-model:visible="drawerVisible"-->
<!--        :operate-type="operateType"-->
<!--        :row-data="editingData"-->
<!--        @submitted="getDataByPage"-->
<!--      />-->
<!--    </NCard>-->

    <BusinessesVideoUploadModule
      v-model:fileList="uploadFileList"
      upload-type="video"
      :uploading="isUploading"
      @upload="handleUpload"
      @preview="handlePreview"
      @remove="handleRemove"
      @show-preview="handleShowPreview"
    />
    <BusinessesVideoFormModule ref="videoFormModuleRef" />

    <BusinessesVideoFormModule>

    </BusinessesVideoFormModule>



  </div>
</template>

<style scoped></style>
