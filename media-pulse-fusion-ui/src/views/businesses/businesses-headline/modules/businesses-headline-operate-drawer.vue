<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreateBusinessesHeadline, fetchUpdateBusinessesHeadline } from '@/service/api/businesses/businesses-headline';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'BusinessesHeadlineOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Businesses.BusinessesHeadline | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增商家文案',
    edit: '编辑商家文案'
  };
  return titles[props.operateType];
});

type Model = Api.Businesses.BusinessesHeadlineOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      title: '',
      content: '',
      status: undefined,
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'content'
  | 'status'
  | 'tenantId'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  content: createRequiredRule('文案内容不能为空'),
  status: createRequiredRule('状态（0-草稿，1-发布，2-下架）不能为空'),
  tenantId: createRequiredRule('租户ID，标记数据所属租户，可通过配置关闭不能为空'),
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, title, content, status } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateBusinessesHeadline({ title, content, status });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateBusinessesHeadline({ id, title, content, status });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="文案标题" path="title">
          <NInput v-model:value="model.title" placeholder="请输入文案标题" />
        </NFormItem>
        <NFormItem label="文案内容" path="content">
          <NInput
            v-model:value="model.content"
            :rows="3"
            type="textarea"
            placeholder="请输入文案内容"
          />
        </NFormItem>
        <NFormItem label="状态（0-草稿，1-发布，2-下架）" path="status">
          <NInput v-model:value="model.status" placeholder="请输入状态（0-草稿，1-发布，2-下架）" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
