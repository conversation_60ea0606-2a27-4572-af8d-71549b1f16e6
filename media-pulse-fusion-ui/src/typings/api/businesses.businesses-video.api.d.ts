/**
 * namespace Businesses
 *
 * backend api module: "Businesses"
 */
namespace Businesses {
    /** businesses video */
    type BusinessesVideo = Common.CommonRecord<{
        /** 主键ID，视频素材唯一标识符 */
        id: CommonType.IdType; 
        /** 视频素材标题或名称 */
        title: string; 
        /** 视频素材详细描述 */
        description: string; 
        /** 视频文件存储的URL或路径，支持OSS/CDN */
        videoUrl: CommonType.IdType; 
        /** 视频素材分类，例如：教学、宣传、娱乐 */
        category: string; 
        /** 素材状态 */
        status: string; 
        /** 租户ID，标记数据所属租户，可通过配置关闭 */
        tenantId: CommonType.IdType; 
        /** 删除标记(0-正常,1-已删除)，逻辑删除字段 */
        delFlag: number; 
    }>;

    /** businesses video search params */
    type BusinessesVideoSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Businesses.BusinessesVideo,
        | 'title'
        | 'description'
        | 'videoUrl'
        | 'category'
        | 'status'
      > &
        Api.Common.CommonSearchParams
    >;

    /** businesses video operate params */
    type BusinessesVideoOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Businesses.BusinessesVideo,
        | 'id'
        | 'title'
        | 'description'
        | 'videoUrl'
        | 'category'
        | 'status'
      >
    >;

    /** businesses video list */
    type BusinessesVideoList = Api.Common.PaginatingQueryRecord<BusinessesVideo>;
}
