import {request} from '@/service/request';

/** 获取视频素材管理列表 */
export function fetchGetBusinessesVideoList(params?: Api.Businesses.BusinessesVideoSearchParams) {
  return request<Api.Businesses.BusinessesVideoList>({
    url: '/businesses/businessesVideo/list',
    method: 'get',
    params
  });
}

/** 新增视频素材管理 */
export function fetchCreateBusinessesVideo(data: Api.Businesses.BusinessesVideoOperateParams) {
  return request<boolean>({
    url: '/businesses/businessesVideo',
    method: 'post',
    data
  });
}

/** 修改视频素材管理 */
export function fetchUpdateBusinessesVideo(data: Api.Businesses.BusinessesVideoOperateParams) {
  return request<boolean>({
    url: '/businesses/businessesVideo',
    method: 'put',
    data
  });
}

/** 批量删除视频素材管理 */
export function fetchBatchDeleteBusinessesVideo(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/businesses/businessesVideo/${ids.join(',')}`,
    method: 'delete'
  });
}


/** 上传视频素材 */
export function fetchUploadVideo(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<Api.SysOssUploadVo>({
    url: '/businesses/businessesVideo/upload',
    method: 'post',
    data: formData,
    timeout: 180000
  });
}

/** 获取当前用户所有视频 */
export function fetchGetVideoList() {
  return request<string[]>({
    url: '/businesses/businessesVideo/getVideoList',
    method: 'get'
  });
}
